/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkListContainerFragment.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/02
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.utils.ViewUtils.addItemDecorationBottom
import com.soundrecorder.playback.R
import com.soundrecorder.wavemark.mark.MarkListAdapter

class MarkListContainerFragment(val fragmentMarkListAdapter: MarkListAdapter? = null)  : COUIPanelFragment() {
    companion object {
        private const val TAG = "MarkListContainerFragment"
        private const val DARK_BG_COLOR = "#FF333333"
        private const val BG_COLOR = "#FFF0F1F2"
    }

    @SuppressLint("InflateParams")
    override fun initView(panelView: View?) {
        toolbar?.visibility = View.GONE
        var markListRecyclerView: COUIRecyclerView? = null
        LayoutInflater.from(activity).inflate(R.layout.fragment_playback_mark_list, null, false).apply {
            findViewById<com.coui.appcompat.toolbar.COUIToolbar>(R.id.mark_list_toolbar)?.apply {
                title = context?.resources?.getString(com.soundrecorder.base.R.string.mark_list)
                isTitleCenterStyle = true
                inflateMenu(R.menu.mark_list_menu)
                menu?.findItem(R.id.cancel).apply {
                    setOnMenuItemClickListener {
                        dismissPanel()
                        setOnMenuItemClickListener(null)
                        true
                    }
                }

                toolbar?.menuView?.setOverflowMenuListener {
                    it.setOnItemClickListener { _, _, _, _ -> it.dismiss() }
                }
            }

            markListRecyclerView = this.findViewById(R.id.mark_list_recycler_view)
            if (markListRecyclerView == null) DebugUtil.i(TAG, "<<markListView is null.")

            markListRecyclerView?.let {
                it.layoutManager = LinearLayoutManager(it.context)
                it.adapter = fragmentMarkListAdapter
                it.addItemDecorationBottom(com.soundrecorder.common.R.dimen.card_margin_top_buttom)
            }

            val dataLength: Int? = fragmentMarkListAdapter?.itemCount
            if (dataLength == 0) DebugUtil.e(TAG, "<<markList data length:" + dataLength)

            (contentView as? ViewGroup)?.addView(this)
        }

        /*
         * Since 15.0, the dragView of panel without handle is hidden by default,
         * and there is no need to actively call this method.
         */
        //hideDragView()
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        var colorValue = context?.getColor(R.color.mark_list_dialog_color)
        DebugUtil.i(
            TAG,
            "Resource color value:$colorValue constant color value:${BG_COLOR.toColorInt()}"
        )
        if (colorValue == null) {
            colorValue = NightModeUtil.isNightMode(context)?.let {
                if (it) DARK_BG_COLOR.toColorInt() else BG_COLOR.toColorInt()
            } ?: BG_COLOR.toColorInt()
        }
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(colorValue)
    }
}
