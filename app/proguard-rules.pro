#-------------------------------------------定制化区域----------------------------------------------
#---------------------------------2.soundrecorder jars---------------------------
-dontwarn com.coloros.commons.**
-keep class com.coloros.commons.** {*;}

-dontwarn com.coui.appcompat.**
-keep class com.coui.appcompat.** {*;}

-dontwarn com.coui.appcompat.**
-keep class com.coui.appcompat.** {*;}

-dontwarn com.coui.appcompat.**
-keep class com.coui.appcompat.** {*;}

-dontwarn oppo.multimedia.soundrecorder.**
-keep class oppo.multimedia.soundrecorder.** {*;}

-dontwarn oplus.multimedia.soundrecorder.**
-keep class oplus.multimedia.soundrecorder.** {*;}

-dontwarn com.oppo.support.**
-keep class com.oppo.support.** {*;}

# backup sdk no need to hunxiao
-keep class com.heytap.backup.sdk.** { *; }
-keep class com.oplus.backup.sdk.** { *; }
# backup sdk no need to hunxiao
-keep class com.coloros.encryption.backuprestore.** { *; }
-keep class com.color.compat.** {*;}
-keep class com.recorder.move.**{*;}
-keep class com.recorder.movepure.**{*;}

# keep iflytek sdk and opus
-keep class com.iflytek.** {*;}
-keep class com.score.rahasak.utils.** {*;}

# keep zoomwindow
-keep public class com.heytap.addon.zoomwindow.**{*;}

#-----------------------------------------
-keep class com.oppo.media.** {*;}
#-------------------------------------------基本不用动区域--------------------------------------------
#---------------------------------基本指令区----------------------------------
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-verbose
-optimizations !code/simplification/cast,!field/*,!class/merging/*
-keepattributes *Annotation*,InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
#----------------------------------------------------------------------------

#---------------------------------默认保留区---------------------------------
-keep public class com.color.commons.view.LinkedEditText$OnSuperLinkClickListener
-keep public class android.widget.ColorOverScroller

-keep public class com.android.vending.licensing.ILicensingService

-keep class android.support.** {*;}

-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

-keepclassmembers class * implements java.io.Serializable {*;}

#-keepclassmembers class * implements java.io.Serializable {
#static final long serialVersionUID;
#    private static final java.io.ObjectStreamField[] serialPersistentFields;
#    private void writeObject(java.io.ObjectOutputStream);
#    private void readObject(java.io.ObjectInputStream);
#    java.lang.Object writeReplace();
#    java.lang.Object readResolve();
#}

-keep class **.R$* {
 *;
}

-keepclassmembers class * {
    void *(**On*Event);
}

-keep class com.oppo.acs.** { *; }
-keep class okio.**{*;}
-keep class com.squareup.wire.**{*;}
-keep public class * extends com.squareup.wire.**{
*;
}
-dontwarn com.oppo.acs.**
-dontwarn okio.**
# Keep methods with Wire annotations (e.g. @ProtoField)
-keepclassmembers class ** {
@com.squareup.wire.ProtoField public *;
@com.squareup.wire.ProtoEnum public *;
}

-keep class com.nearme.** {*;}

-keep class com.coui.appcompat.widget.** {*;}
-dontwarn com.android.mkstubs.**
-dontwarn com.color.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**

-keep class android.app.**{*;}
-keep class android.batterySipper.**{*;}
-keep class android.common.**{*;}
-keep class android.content.**{*;}
-keep class android.graphics.**{*;}
-keep class android.hardware.**{*;}
-keep class android.inputmethodservice.**{*;}
-keep class android.net.**{*;}
-keep class android.os.**{*;}
-keep class android.provider.oppo.**{*;}
-keep class android.security.**{*;}
-keep class android.service.notification.**{*;}
-keep class android.telephony.**{*;}
-keep class android.text.**{*;}
-keep class android.view.**{*;}
-keep class android.widget.**{*;}
-keep class cn.teddymobile.free.anteater.**{*;}
-keep class com.android.id.impl.**{*;}
-keep class com.color.**{*;}
-keep class com.coloros.**{*;}
-keep class com.oppo.**{*;}
-keep class oppo.android.**{*;}
-keep class oppo.app.**{*;}
-keep class oppo.content.res.**{*;}
-keep class oppo.net.wifi.**{*;}
-keep class oppo.util.**{*;}
#-keep class com.oplus.**{*;}  // to fix safe bug 2153757

# must add for record, no add this media will crash when record
-keep class com.oplus.media.**{*;}
-keep class com.soundrecorder.base.**{*;}
-keep class com.soundrecorder.common.**{*;}
-keep class com.soundrecorder.imageload.**{*;}
-keep class com.soundrecorder.convertservice.**{*;}
-keep class com.oplus.ainm.**{*;}
-keep class com.oplus.compat.app.ActivityNative
-keep class com.oplus.oiface.**{*;}
-keep class com.oplus.compat.os.SystemPropertiesNative {*;}
-keep class com.oplus.compat.os.UserManagerNative
-keep class com.oplus.coreapp.appfeature.AppFeatureProviderUtils
-keep class com.oplus.compat.view.WindowManagerNative

# Keep class for support 8.2.1
-dontwarn com.android.mkstubs.**
-dontwarn com.oplus.inner.**
-dontwarn com.color.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**
-dontwarn com.oplus.compat.**
-dontwarn com.oplus.epona.internal.LoggerSnapshot
-dontwarn com.oplus.epona.internal.LoggerSnapShotOplusCompat
-dontwarn com.oplus.compat.**
-dontwarn android.telephony.**
-keep class mirror.android.** {*;}
-keep class com.heytap.reflect.** {*;}
-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}
-keep class com.oplus.os.OplusBuild {*;}
-keep class com.oplus.os.OplusBuild.** {*;}

-keep class com.heytap.epona.ExceptionInfo {*;}

-keepclassmembers class com.heytap.compat.**.*Native{
     static com.heytap.reflect.Ref* *;
}
-keepclassmembers class com.heytap.compat.**.*Native$* {
     static com.heytap.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat$* {
 static com.oplus.utils.reflect.Ref* *;
}
# CloudConfig
-keep @com.oplus.baselib.database.annotation.DbEntity class * {*;}
-keepclassmembers class * {
    @com.oplus.nearx.cloudconfig.anotation.FieldIndex  *;
}
-keep @androidx.anntotation.Keep class **

-keep class com.oplus.compat.app.ActivityManagerNative$ProcessObserver { *;}

-keep class com.oplus.compat.app.ActivityManagerNative$PackageDataObserver{ *;}

-keep public class com.oplus.compat.fingerprint.FingerprintNative { *; }

#----------------------------------------------------------------------------

#enpona keep
-keepattributes *Annotation*
-keep @com.oplus.epona.annotation.Provider class * {*;}
-keep @interface com.oplus.epona.annotation.Provider
-keepclassmembers class *{
    @com.oplus.epona.annotation.Provider *;
}
-keep @com.oplus.epona.annotation.Action class * {*;}
-keep @interface com.oplus.epona.annotation.Action
-keepclassmembers class * {
    @com.oplus.epona.annotation.Action *;
}

-dontwarn com.squareup.javapoet.**
-dontwarn com.google.common.reflect.**
-dontwarn com.google.auto.service.processor.**
-dontwarn com.google.auto.common.**
-dontwarn com.oplus.epona.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.common.util.concurrent.**
-dontwarn com.squareup.javapoet.**

-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}
-keep class com.oplus.os.OplusBuild {*;}
-keep class com.oplus.os.OplusBuild.** {*;}

-keep class com.heytap.epona.ExceptionInfo {*;}

#add for stdidsdk
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }
#----------------------------------------------------------------------------


-dontwarn com.google.**
-keep class com.google.** {
    *;
}


# Retrofit2
-dontwarn okio.**
-dontwarn retrofit2.Platform$Java8

-ignorewarnings

# XLog start
#-dontshrink
#保留注解参数
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-dontwarn  io.protostuff.Tag

# 外部调用到的类
-keep public class com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto { *; }
-keep public class com.oplus.log.uploader.UploadManager { *; }
-keep public class com.oplus.log.uploader.UploadManager$* { *; }
-keep public class com.oplus.log.Logger { *; }
-keep public class com.oplus.log.Logger$* { *; }
-keep public class com.oplus.log.Settings { *; }
-keep public class com.oplus.log.Settings* { *; }
-keep public class com.oplus.log.ILog { *; }
-keep public class com.oplus.log.consts.LogLevel { *; }
-keep public class com.oplus.log.log.AndroidLog { *; }
-keep public class com.oplus.log.uploader.IHttpDelegate { *; }
-keep public class com.oplus.log.uploader.ResponseWrapper { *; }
-keep public class com.oplus.log.ISimpleLog { *; }
-keep public class com.oplus.log.consts.BusinessType { *; }
-keep public class com.oplus.log.consts.LogLevel { *; }

# native方法
-keep class com.oplus.log.core.CLoganProtocol { *; }
# XLog end

#速览负一屏 start
-keep class com.oplus.cardwidget.proto.UIDataProto** {*;}
#速览负一屏 end

#账号ID SDK混淆规则 老SDK需要的混淆（新版按下面一样配置）
-keep class com.nearme.aidl.** { *; }
-keep class * implements com.platform.usercenter.annotation.NoProguard {
  public *;
}
-keep class com.heytap.usercenter.accountsdk.BuildConfig { *; }
-keep class com.platform.usercenter.annotation.Keep
-keep @com.platform.usercenter.annotation.Keep class * {*;}
-keep class com.heytap.uccompiler.annotation.interceptor.** { *; }
-keep class com.accountbase.** { *; }
-keep class com.platform.usercenter.**{*;}
-keep class com.platform.usercenter.basic.annotation.Keep
-keep @com.platform.usercenter.basic.annotation.Keep class * {*;}

-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class *
-keep class android.content.pm.**{*;}
# 校验系统
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# ipc
-keep public abstract interface * extends android.os.IInterface{*;}
-keep public abstract class * extends android.os.Binder{*;}

-keep class com.accountservice.** { *; }

# coverage
-keep class com.autotest.opasm.**{*;}
# cloud config
-keep class com.oplus.nearx.**{*;}